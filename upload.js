class FileUploader {
    constructor() {
        // 动态切片大小：根据文件大小调整
        this.getChunkSize = (fileSize) => {
            if (fileSize < 50 * 1024 * 1024) {        // < 50MB: 2MB切片
                return 2 * 1024 * 1024;
            } else if (fileSize < 200 * 1024 * 1024) { // 50MB-200MB: 5MB切片
                return 5 * 1024 * 1024;
            } else if (fileSize < 1024 * 1024 * 1024) { // 200MB-1GB: 10MB切片
                return 10 * 1024 * 1024;
            } else {                                   // > 1GB: 20MB切片
                return 20 * 1024 * 1024;
            }
        };

        // 动态并发数：根据文件大小调整
        this.getConcurrentUploads = (fileSize) => {
            if (fileSize < 100 * 1024 * 1024) {        // < 100MB: 3个并发
                return 3;
            } else if (fileSize < 500 * 1024 * 1024) { // 100MB-500MB: 5个并发
                return 5;
            } else {                                   // > 500MB: 6个并发
                return 6;
            }
        };

        this.uploadQueue = [];
        this.activeUploads = new Set();

        this.initEventListeners();
        this.loadUploadedFiles();
    }

    initEventListeners() {
        const uploadZone = document.getElementById('uploadZone');
        const fileInput = document.getElementById('fileInput');
        const refreshBtn = document.getElementById('refreshBtn');

        // 文件选择
        fileInput.addEventListener('change', (e) => {
            this.handleFiles(e.target.files);
        });

        // 拖拽上传
        uploadZone.addEventListener('dragover', (e) => {
            e.preventDefault();
            uploadZone.classList.add('dragover');
        });

        uploadZone.addEventListener('dragleave', () => {
            uploadZone.classList.remove('dragover');
        });

        uploadZone.addEventListener('drop', (e) => {
            e.preventDefault();
            uploadZone.classList.remove('dragover');
            this.handleFiles(e.dataTransfer.files);
        });

        // 刷新文件列表
        refreshBtn.addEventListener('click', () => {
            this.loadUploadedFiles();
        });
    }

    handleFiles(files) {
        Array.from(files).forEach(file => {
            this.uploadFile(file);
        });
    }

    async uploadFile(file) {
        const hash = await this.calculateFileHash(file);
        const uploadId = `upload-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`;

        // 创建上传项UI
        const uploadItem = this.createUploadItem(file, uploadId);
        document.getElementById('uploadList').appendChild(uploadItem);

        // 检查文件是否已存在
        try {
            const checkResult = await this.checkFileExists(hash, file.name);
            if (checkResult.exists) {
                this.updateUploadStatus(uploadId, '文件已存在', 'success');
                this.updateProgress(uploadId, 100);
                return;
            }

            // 开始上传
            await this.startChunkedUpload(file, hash, uploadId, checkResult.uploaded || []);

        } catch (error) {
            console.error('上传失败:', error);
            this.updateUploadStatus(uploadId, `上传失败: ${error.message}`, 'error');
        }
    }

    async calculateFileHash(file) {
        return new Promise((resolve) => {
            const reader = new FileReader();
            reader.onload = (e) => {
                const buffer = e.target.result;
                const hashBuffer = new Uint8Array(buffer);
                let hash = 0;
                for (let i = 0; i < hashBuffer.length; i++) {
                    hash = ((hash << 5) - hash + hashBuffer[i]) & 0xffffffff;
                }
                resolve(Math.abs(hash).toString(36));
            };
            reader.readAsArrayBuffer(file.slice(0, Math.min(file.size, 1024 * 1024))); // 只读取前1MB计算hash
        });
    }

    async checkFileExists(hash, filename) {
        const response = await fetch('/check-file', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ hash, filename })
        });
        return await response.json();
    }

    async startChunkedUpload(file, hash, uploadId, uploadedChunks = []) {
        const chunkSize = this.getChunkSize(file.size);
        const concurrentUploads = this.getConcurrentUploads(file.size);
        const totalChunks = Math.ceil(file.size / chunkSize);
        const uploadedSet = new Set(uploadedChunks);

        this.updateChunkInfo(uploadId, uploadedChunks.length, totalChunks);
        this.updateFileStrategy(uploadId, chunkSize, concurrentUploads);

        const startTime = Date.now();

        // 创建上传任务队列
        const uploadTasks = [];
        for (let i = 0; i < totalChunks; i++) {
            if (!uploadedSet.has(i)) {
                uploadTasks.push({
                    file,
                    hash,
                    index: i,
                    totalChunks,
                    uploadId,
                    startTime,
                    chunkSize,
                    concurrentUploads
                });
            }
        }

        // 并发上传
        const results = await this.uploadChunksConcurrently(uploadTasks, concurrentUploads);

        // 检查是否所有切片都上传成功
        const successCount = results.filter(r => r.success).length;
        if (successCount === uploadTasks.length) {
            // 合并文件
            await this.mergeFile(hash, file.name, uploadId);
        } else {
            throw new Error(`部分切片上传失败: ${successCount}/${uploadTasks.length}`);
        }
    }

    async uploadChunksConcurrently(tasks, concurrentUploads) {
        const results = [];
        const executing = [];

        for (const task of tasks) {
            const promise = this.uploadChunk(task).then(result => {
                executing.splice(executing.indexOf(promise), 1);
                return result;
            });

            results.push(promise);
            executing.push(promise);

            if (executing.length >= concurrentUploads) {
                await Promise.race(executing);
            }
        }

        return Promise.all(results);
    }

    async uploadChunk(task) {
        const { file, hash, index, totalChunks, uploadId, chunkSize } = task;
        const start = index * chunkSize;
        const end = Math.min(start + chunkSize, file.size);
        const chunk = file.slice(start, end);

        const formData = new FormData();
        formData.append('chunk', chunk);
        formData.append('hash', hash);
        formData.append('index', index);
        formData.append('total', totalChunks);
        formData.append('filename', file.name);

        try {
            const response = await fetch('/upload-chunk', {
                method: 'POST',
                body: formData
            });

            const result = await response.json();

            if (result.success) {
                // 更新进度
                const progress = (result.uploaded / result.total) * 100;
                this.updateProgress(uploadId, progress);
                this.updateChunkInfo(uploadId, result.uploaded, result.total);

                // 更新上传速度
                const elapsed = (Date.now() - task.startTime) / 1000;
                const speed = (result.uploaded * chunkSize) / elapsed;
                this.updateUploadSpeed(uploadId, speed);

                return { success: true, index };
            } else {
                throw new Error(result.error);
            }
        } catch (error) {
            console.error(`切片 ${index} 上传失败:`, error);
            return { success: false, index, error };
        }
    }

    async mergeFile(hash, filename, uploadId) {
        this.updateUploadStatus(uploadId, '正在合并文件...', '');

        try {
            const response = await fetch('/merge-file', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ hash, filename })
            });

            const result = await response.json();

            if (result.success) {
                this.updateUploadStatus(uploadId, '上传完成', 'success');
                this.loadUploadedFiles(); // 刷新文件列表
            } else {
                throw new Error(result.error);
            }
        } catch (error) {
            throw new Error(`文件合并失败: ${error.message}`);
        }
    }

    createUploadItem(file, uploadId) {
        const div = document.createElement('div');
        div.className = 'upload-item';
        div.id = uploadId;

        div.innerHTML = `
            <div class="file-info">
                <span class="file-name">${file.name}</span>
                <span class="file-size">${this.formatFileSize(file.size)}</span>
            </div>
            <div class="progress-container">
                <div class="progress-bar"></div>
            </div>
            <div class="upload-status">
                <span class="status-text">准备上传...</span>
                <span class="upload-speed"></span>
            </div>
            <div class="chunk-info"></div>
        `;

        return div;
    }

    updateProgress(uploadId, percentage) {
        const progressBar = document.querySelector(`#${uploadId} .progress-bar`);
        if (progressBar) {
            progressBar.style.width = `${percentage}%`;
        }
    }

    updateUploadStatus(uploadId, status, className = '') {
        const statusText = document.querySelector(`#${uploadId} .status-text`);
        if (statusText) {
            statusText.textContent = status;
            statusText.className = `status-text ${className}`;
        }
    }

    updateUploadSpeed(uploadId, bytesPerSecond) {
        const speedElement = document.querySelector(`#${uploadId} .upload-speed`);
        if (speedElement) {
            speedElement.textContent = `${this.formatFileSize(bytesPerSecond)}/s`;
        }
    }

    updateChunkInfo(uploadId, uploaded, total) {
        const chunkInfo = document.querySelector(`#${uploadId} .chunk-info`);
        if (chunkInfo) {
            chunkInfo.textContent = `切片进度: ${uploaded}/${total}`;
        }
    }

    updateFileStrategy(uploadId, chunkSize, concurrentUploads) {
        const chunkInfo = document.querySelector(`#${uploadId} .chunk-info`);
        if (chunkInfo) {
            const strategyText = `策略: ${this.formatFileSize(chunkSize)}/片, ${concurrentUploads}并发`;
            chunkInfo.innerHTML = `${chunkInfo.textContent}<br><small style="color: #888;">${strategyText}</small>`;
        }
    }

    async loadUploadedFiles() {
        try {
            const response = await fetch('/uploads');
            const files = await response.json();

            const fileList = document.getElementById('fileList');
            fileList.innerHTML = '';

            if (files.length === 0) {
                fileList.innerHTML = '<p style="color: #666; text-align: center; padding: 20px;">暂无上传文件</p>';
                return;
            }

            files.forEach(file => {
                const fileItem = document.createElement('div');
                fileItem.className = 'file-item';

                fileItem.innerHTML = `
                    <div class="file-item-info">
                        <div class="file-item-name">${file.name}</div>
                        <div class="file-item-details">
                            大小: ${this.formatFileSize(file.size)} |
                            上传时间: ${new Date(file.uploadTime).toLocaleString()}
                        </div>
                    </div>
                `;

                fileList.appendChild(fileItem);
            });
        } catch (error) {
            console.error('加载文件列表失败:', error);
        }
    }

    formatFileSize(bytes) {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }
}

// 初始化上传器
document.addEventListener('DOMContentLoaded', () => {
    new FileUploader();
});
