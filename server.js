const express = require('express');
const multer = require('multer');
const cors = require('cors');
const crypto = require('crypto');
const fs = require('fs-extra');
const path = require('path');

const app = express();
const PORT = 3000;

// 中间件
app.use(cors());
app.use(express.json({ limit: '50mb' })); // 增加JSON请求体大小限制
app.use(express.urlencoded({ limit: '50mb', extended: true })); // 增加URL编码请求体大小限制
app.use(express.static('.')); // 提供静态文件服务

// 创建上传目录
const uploadDir = path.join(__dirname, 'uploads');
const tempDir = path.join(__dirname, 'temp');
fs.ensureDirSync(uploadDir);
fs.ensureDirSync(tempDir);

// 配置 multer
const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    cb(null, tempDir);
  },
  filename: (req, file, cb) => {
    const { hash, index } = req.body;
    cb(null, `${hash}-${index}`);
  }
});

const upload = multer({
  storage,
  limits: {
    fileSize: 50 * 1024 * 1024 // 50MB per chunk limit
  }
});

// 存储上传状态
const uploadStatus = new Map();

// 上传切片
app.post('/upload-chunk', upload.single('chunk'), async (req, res) => {
  try {
    const { hash, index, total, filename } = req.body;

    if (!req.file) {
      return res.status(400).json({ error: '没有接收到文件切片' });
    }

    // 初始化上传状态
    if (!uploadStatus.has(hash)) {
      uploadStatus.set(hash, {
        filename,
        total: parseInt(total),
        uploaded: new Set(),
        chunks: []
      });
    }

    const status = uploadStatus.get(hash);
    status.uploaded.add(parseInt(index));
    status.chunks[parseInt(index)] = req.file.path;

    console.log(`接收切片: ${filename} - ${index}/${total}`);

    res.json({
      success: true,
      message: '切片上传成功',
      uploaded: status.uploaded.size,
      total: status.total
    });

  } catch (error) {
    console.error('上传切片错误:', error);
    res.status(500).json({ error: '服务器错误' });
  }
});

// 合并文件
app.post('/merge-file', async (req, res) => {
  try {
    const { hash, filename } = req.body;

    if (!uploadStatus.has(hash)) {
      return res.status(400).json({ error: '找不到上传记录' });
    }

    const status = uploadStatus.get(hash);

    // 检查是否所有切片都已上传
    if (status.uploaded.size !== status.total) {
      return res.status(400).json({
        error: '文件切片不完整',
        uploaded: status.uploaded.size,
        total: status.total
      });
    }

    const finalPath = path.join(uploadDir, filename);
    const writeStream = fs.createWriteStream(finalPath);

    // 按顺序合并切片
    for (let i = 0; i < status.total; i++) {
      const chunkPath = status.chunks[i];
      if (!chunkPath || !fs.existsSync(chunkPath)) {
        throw new Error(`切片 ${i} 不存在`);
      }

      const chunkBuffer = await fs.readFile(chunkPath);
      writeStream.write(chunkBuffer);

      // 删除临时切片文件
      await fs.remove(chunkPath);
    }

    writeStream.end();

    // 清理上传状态
    uploadStatus.delete(hash);

    console.log(`文件合并完成: ${filename}`);

    res.json({
      success: true,
      message: '文件上传完成',
      filename,
      path: finalPath
    });

  } catch (error) {
    console.error('合并文件错误:', error);
    res.status(500).json({ error: '文件合并失败' });
  }
});

// 检查文件是否已存在
app.post('/check-file', (req, res) => {
  try {
    const { hash, filename } = req.body;
    const filePath = path.join(uploadDir, filename);

    if (fs.existsSync(filePath)) {
      return res.json({
        exists: true,
        message: '文件已存在'
      });
    }

    // 检查是否有部分上传的切片
    const status = uploadStatus.get(hash);
    if (status) {
      return res.json({
        exists: false,
        uploaded: Array.from(status.uploaded),
        total: status.total
      });
    }

    res.json({ exists: false });

  } catch (error) {
    console.error('检查文件错误:', error);
    res.status(500).json({ error: '服务器错误' });
  }
});

// 获取上传列表
app.get('/uploads', async (req, res) => {
  try {
    const files = await fs.readdir(uploadDir);
    const fileList = [];

    for (const file of files) {
      const filePath = path.join(uploadDir, file);
      const stats = await fs.stat(filePath);
      fileList.push({
        name: file,
        size: stats.size,
        uploadTime: stats.mtime
      });
    }

    res.json(fileList);
  } catch (error) {
    console.error('获取文件列表错误:', error);
    res.status(500).json({ error: '服务器错误' });
  }
});

app.listen(PORT, () => {
  console.log(`服务器运行在 http://localhost:${PORT}`);
});
