<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>文件切片上传</title>
    <link rel="stylesheet" href="style.css">
</head>
<body>
    <div class="container">
        <h1>文件切片上传</h1>

        <div class="upload-area">
            <div class="upload-zone" id="uploadZone">
                <div class="upload-icon">📁</div>
                <p>点击选择文件或拖拽文件到此处</p>
                <p style="font-size: 0.9em; color: #888; margin-top: 10px;">
                    智能切片策略：<br>
                    • &lt;50MB: 2MB切片, 3并发<br>
                    • 50MB-200MB: 5MB切片, 5并发<br>
                    • 200MB-1GB: 10MB切片, 5并发<br>
                    • &gt;1GB: 20MB切片, 6并发
                </p>
                <input type="file" id="fileInput" multiple>
            </div>
        </div>

        <div class="upload-list" id="uploadList"></div>

        <div class="uploaded-files">
            <h2>已上传文件</h2>
            <div id="fileList"></div>
            <button id="refreshBtn" class="btn">刷新列表</button>
        </div>
    </div>

    <script src="upload.js"></script>
</body>
</html>
